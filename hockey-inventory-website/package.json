{"name": "hockey-inventory-browser", "version": "1.0.0", "description": "Hockey Equipment Inventory Browser with Lightspeed Integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"axios": "^1.6.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "jose": "^4.14.4", "openid-client": "^5.6.5"}, "devDependencies": {"@21st-extension/toolbar": "^0.5.6", "@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@stagewise/toolbar": "^0.4.9", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^6.1.5", "@types/express": "^4.17.21", "@types/express-session": "^1.17.9", "@types/jsdom": "^21.1.7", "@types/node": "^20.10.5", "babel-jest": "^30.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}