{"name": "openshift-sku-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bun run prepare-data && vite", "build": "bun run prepare-data && tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "prepare-data": "bun run scripts/prepare-data.ts"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "jsdom": "^26.1.0", "papaparse": "^5.5.3", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.35.0", "vite": "^5.4.19", "vitest": "^3.2.4"}}